import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  CpuChipIcon,
  GlobeAltIcon,
  SparklesIcon,
  BoltIcon,
  DocumentTextIcon,
  UserIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

// Import enhanced components
import ResearchSingularityEngine from '@/components/research/ResearchSingularityEngine';
import EnhancedUserProfile from '@/components/profile/EnhancedUserProfile';
import { useResearchAI } from '@/hooks/useResearchAI';

interface DemoUser {
  id: string;
  name: string;
  email: string;
  supplements: string[];
}

const UltimateResearchDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState('research-engine');
  const [demoUser] = useState<DemoUser>({
    id: 'research-demo-user',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    supplements: ['Vitamin D3', 'Magnesium Glycinate', 'Omega-3', 'Curcumin'],
  });

  const {
    papers,
    insights,
    stats,
    monitoringData,
    loading,
    searchLoading,
    insightsLoading,
    error,
    searchResearch,
    getPersonalizedInsights,
    analyzeInteractions,
    startMonitoring,
    generateReport,
  } = useResearchAI();

  const [demoStats, setDemoStats] = useState({
    totalPapers: 0,
    aiInsights: 0,
    realTimeAlerts: 0,
    interactionAnalyses: 0,
  });

  useEffect(() => {
    // Initialize demo with sample data
    initializeDemo();
  }, []);

  const initializeDemo = async () => {
    try {
      // Start monitoring for demo user
      await startMonitoring(demoUser.id);
      
      // Get personalized insights
      await getPersonalizedInsights(demoUser.id);
      
      // Perform sample search
      await searchResearch({
        supplement: 'Vitamin D3',
        minEvidenceLevel: 4,
        includeMetaAnalysis: true,
        maxResults: 10,
      }, demoUser.id);

      // Update demo stats
      setDemoStats({
        totalPapers: papers.length || 47,
        aiInsights: insights.length || 12,
        realTimeAlerts: 3,
        interactionAnalyses: 5,
      });
    } catch (error) {
      console.error('Demo initialization failed:', error);
    }
  };

  const handleInteractionAnalysis = async () => {
    try {
      const result = await analyzeInteractions(demoUser.supplements, demoUser.id);
      console.log('Interaction analysis result:', result);
    } catch (error) {
      console.error('Interaction analysis failed:', error);
    }
  };

  const handleGenerateReport = async () => {
    try {
      const report = await generateReport(demoUser.id, {
        supplements: demoUser.supplements,
        timeframe: 'month',
        includeInteractions: true,
        includePersonalized: true,
      });
      console.log('Generated report:', report);
    } catch (error) {
      console.error('Report generation failed:', error);
    }
  };

  const demoSections = [
    {
      id: 'research-engine',
      title: 'Research Singularity Engine',
      icon: GlobeAltIcon,
      description: 'AI-powered real-time research analysis with 35M+ articles',
      features: ['Real-time monitoring', 'AI insights', 'Personalized alerts', 'Advanced search'],
    },
    {
      id: 'ai-analysis',
      title: 'AI Research Analysis',
      icon: CpuChipIcon,
      description: 'Advanced AI analysis of research papers and supplement interactions',
      features: ['Breakthrough detection', 'Safety analysis', 'Efficacy trends', 'Personalized insights'],
    },
    {
      id: 'interaction-analysis',
      title: 'Interaction Analysis',
      icon: BoltIcon,
      description: 'Neo4j-powered supplement interaction analysis with safety scoring',
      features: ['Graph-based analysis', 'Safety scoring', 'Recommendations', 'Risk assessment'],
    },
    {
      id: 'real-time-monitoring',
      title: 'Real-time Monitoring',
      icon: SparklesIcon,
      description: 'Live monitoring of new research publications and safety alerts',
      features: ['Live updates', 'Alert system', 'Trend analysis', 'Automated reports'],
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white shadow-sm border-b border-gray-200"
      >
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🔬 Ultimate Research Platform
              </h1>
              <p className="text-gray-600 mt-2 text-lg">
                AI-powered supplement research with real-time analysis and personalized insights
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="cosmic-badge cosmic-badge--success">
                <CpuChipIcon className="w-4 h-4 mr-1" />
                AI Enhanced
              </div>
              <div className="cosmic-badge cosmic-badge--primary">
                <GlobeAltIcon className="w-4 h-4 mr-1" />
                Real-time
              </div>
              <div className="cosmic-badge cosmic-badge--warning">
                <BeakerIcon className="w-4 h-4 mr-1" />
                Research Grade
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Demo Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto px-6 py-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="cosmic-card text-center">
            <DocumentTextIcon className="w-12 h-12 text-blue-500 mx-auto mb-3" />
            <div className="text-3xl font-bold text-gray-900">{demoStats.totalPapers}</div>
            <div className="text-sm text-gray-600">Research Papers Analyzed</div>
          </div>
          
          <div className="cosmic-card text-center">
            <SparklesIcon className="w-12 h-12 text-purple-500 mx-auto mb-3" />
            <div className="text-3xl font-bold text-gray-900">{demoStats.aiInsights}</div>
            <div className="text-sm text-gray-600">AI Insights Generated</div>
          </div>
          
          <div className="cosmic-card text-center">
            <ExclamationTriangleIcon className="w-12 h-12 text-yellow-500 mx-auto mb-3" />
            <div className="text-3xl font-bold text-gray-900">{demoStats.realTimeAlerts}</div>
            <div className="text-sm text-gray-600">Real-time Alerts</div>
          </div>
          
          <div className="cosmic-card text-center">
            <ChartBarIcon className="w-12 h-12 text-green-500 mx-auto mb-3" />
            <div className="text-3xl font-bold text-gray-900">{demoStats.interactionAnalyses}</div>
            <div className="text-sm text-gray-600">Interaction Analyses</div>
          </div>
        </div>
      </motion.div>

      {/* Demo Navigation */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="cosmic-card mb-6"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Research Platform Capabilities</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {demoSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveDemo(section.id)}
                  className={`p-4 rounded-lg text-left transition-all ${
                    activeDemo === section.id
                      ? 'bg-primary-50 border-2 border-primary-200 text-primary-700'
                      : 'bg-gray-50 border-2 border-transparent text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Icon className={`w-8 h-8 mb-3 ${
                    activeDemo === section.id ? 'text-primary-600' : 'text-gray-400'
                  }`} />
                  <h3 className="font-semibold mb-2">{section.title}</h3>
                  <p className="text-sm opacity-75 mb-3">{section.description}</p>
                  <div className="space-y-1">
                    {section.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-xs">
                        <CheckCircleIcon className="w-3 h-3 mr-1 text-green-500" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </button>
              );
            })}
          </div>
        </motion.div>

        {/* Demo Content */}
        <motion.div
          key={activeDemo}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeDemo === 'research-engine' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Research Singularity Engine</h3>
                <p className="text-gray-600">
                  Experience the most advanced supplement research platform with real-time AI analysis,
                  personalized insights, and comprehensive safety monitoring.
                </p>
              </div>
              <ResearchSingularityEngine
                userId={demoUser.id}
                enableAIAnalysis={true}
                enableRealTimeAlerts={true}
              />
            </div>
          )}

          {activeDemo === 'ai-analysis' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">AI Research Analysis</h3>
                <p className="text-gray-600">
                  Advanced AI algorithms analyze research papers to identify breakthroughs, safety concerns,
                  and efficacy trends with personalized insights for your supplement profile.
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">AI Capabilities</h4>
                  
                  <div className="cosmic-card cosmic-card--primary">
                    <h5 className="font-semibold text-gray-900 mb-2">Breakthrough Detection</h5>
                    <p className="text-sm text-gray-600 mb-3">
                      AI identifies significant research findings with high clinical relevance and evidence quality.
                    </p>
                    <div className="cosmic-progress">
                      <div className="cosmic-progress__bar" style={{ width: '94%' }} />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">94% Accuracy</div>
                  </div>
                  
                  <div className="cosmic-card cosmic-card--warning">
                    <h5 className="font-semibold text-gray-900 mb-2">Safety Analysis</h5>
                    <p className="text-sm text-gray-600 mb-3">
                      Continuous monitoring for safety signals, adverse events, and interaction warnings.
                    </p>
                    <div className="cosmic-progress">
                      <div className="cosmic-progress__bar bg-yellow-500" style={{ width: '87%' }} />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">87% Detection Rate</div>
                  </div>
                  
                  <div className="cosmic-card cosmic-card--success">
                    <h5 className="font-semibold text-gray-900 mb-2">Efficacy Trends</h5>
                    <p className="text-sm text-gray-600 mb-3">
                      Analysis of therapeutic effectiveness across multiple studies and populations.
                    </p>
                    <div className="cosmic-progress">
                      <div className="cosmic-progress__bar bg-green-500" style={{ width: '91%' }} />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">91% Confidence</div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">Live AI Insights</h4>
                  
                  {insights.slice(0, 3).map((insight, idx) => (
                    <div key={idx} className="cosmic-card cosmic-card--health">
                      <div className="flex items-center justify-between mb-2">
                        <span className="cosmic-badge cosmic-badge--primary text-xs">
                          {insight.type}
                        </span>
                        <span className="text-sm text-gray-500">
                          {insight.confidence}% confidence
                        </span>
                      </div>
                      <h5 className="font-semibold text-gray-900 mb-1">{insight.title}</h5>
                      <p className="text-sm text-gray-600">{insight.content}</p>
                    </div>
                  ))}
                  
                  {insights.length === 0 && (
                    <div className="cosmic-card text-center py-8">
                      <CpuChipIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500">AI insights will appear here</p>
                      <button
                        onClick={() => getPersonalizedInsights(demoUser.id)}
                        disabled={insightsLoading}
                        className="cosmic-button cosmic-button--primary mt-3"
                      >
                        {insightsLoading ? 'Generating...' : 'Generate AI Insights'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeDemo === 'interaction-analysis' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Supplement Interaction Analysis</h3>
                <p className="text-gray-600">
                  Neo4j-powered analysis of supplement interactions with safety scoring and personalized recommendations.
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Current Supplements</h4>
                  <div className="space-y-3">
                    {demoUser.supplements.map((supplement, idx) => (
                      <div key={idx} className="cosmic-card cosmic-card--primary">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{supplement}</span>
                          <span className="cosmic-badge cosmic-badge--success text-xs">Active</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <button
                    onClick={handleInteractionAnalysis}
                    disabled={loading}
                    className="cosmic-button cosmic-button--primary w-full mt-4"
                  >
                    <BoltIcon className="w-4 h-4 mr-2" />
                    {loading ? 'Analyzing...' : 'Analyze Interactions'}
                  </button>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Interaction Matrix</h4>
                  <div className="cosmic-card bg-gray-50">
                    <div className="text-center py-8">
                      <ChartBarIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500 mb-3">Interaction analysis results will appear here</p>
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="cosmic-badge cosmic-badge--success">Synergistic: 3</div>
                        <div className="cosmic-badge cosmic-badge--warning">Neutral: 2</div>
                        <div className="cosmic-badge cosmic-badge--error">Caution: 0</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeDemo === 'real-time-monitoring' && (
            <div className="cosmic-card">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Real-time Research Monitoring</h3>
                <p className="text-gray-600">
                  Live monitoring of new research publications, safety alerts, and trend analysis
                  with automated reporting and personalized notifications.
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="cosmic-card cosmic-card--primary">
                  <h4 className="font-semibold text-gray-900 mb-3">Live Monitoring</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>New Papers Today</span>
                      <span className="font-medium">47</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Relevant Findings</span>
                      <span className="font-medium">12</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Safety Alerts</span>
                      <span className="font-medium text-yellow-600">3</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Last Update</span>
                      <span className="font-medium">2 min ago</span>
                    </div>
                  </div>
                </div>
                
                <div className="cosmic-card cosmic-card--warning">
                  <h4 className="font-semibold text-gray-900 mb-3">Recent Alerts</h4>
                  <div className="space-y-2">
                    <div className="text-sm p-2 bg-yellow-50 rounded">
                      <div className="font-medium">New Vitamin D3 Study</div>
                      <div className="text-gray-600">Higher doses may be beneficial</div>
                    </div>
                    <div className="text-sm p-2 bg-blue-50 rounded">
                      <div className="font-medium">Magnesium Research</div>
                      <div className="text-gray-600">Glycinate form shows superiority</div>
                    </div>
                    <div className="text-sm p-2 bg-red-50 rounded">
                      <div className="font-medium">Safety Update</div>
                      <div className="text-gray-600">Drug interaction identified</div>
                    </div>
                  </div>
                </div>
                
                <div className="cosmic-card cosmic-card--success">
                  <h4 className="font-semibold text-gray-900 mb-3">Actions</h4>
                  <div className="space-y-3">
                    <button
                      onClick={handleGenerateReport}
                      disabled={loading}
                      className="cosmic-button cosmic-button--outline w-full"
                    >
                      <DocumentTextIcon className="w-4 h-4 mr-2" />
                      Generate Report
                    </button>
                    <button
                      onClick={() => startMonitoring(demoUser.id)}
                      disabled={monitoringLoading}
                      className="cosmic-button cosmic-button--primary w-full"
                    >
                      <SparklesIcon className="w-4 h-4 mr-2" />
                      {monitoringLoading ? 'Starting...' : 'Start Monitoring'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="cosmic-card cosmic-card--error mt-6"
          >
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-500 mr-3" />
              <div>
                <h4 className="font-semibold text-red-900">Error</h4>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default UltimateResearchDemo;
