import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useResearchAI } from '@/hooks/useResearchAI';
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  BeakerIcon,
  ChartBarIcon,
  ClockIcon,
  GlobeAltIcon,
  CheckBadgeIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';

interface ResearchSource {
  id: string;
  name: string;
  type: 'pubmed' | 'cochrane' | 'clinicaltrials' | 'fda' | 'ema' | 'health_canada';
  articles: number;
  lastUpdate: Date;
  status: 'active' | 'processing' | 'error';
  processingSpeed: number; // articles per second
}

interface ResearchArticle {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  year: number;
  pmid?: string;
  doi?: string;
  abstract: string;
  relevanceScore: number;
  qualityScore: number;
  extractedData: ExtractedData;
  processingTime: number;
  confidence: number;
}

interface ExtractedData {
  supplements: string[];
  conditions: string[];
  outcomes: string[];
  dosages: string[];
  sideEffects: string[];
  interactions: string[];
  studyType: 'rct' | 'observational' | 'meta_analysis' | 'review' | 'case_study';
  participants: number;
  duration: string;
  conclusions: string[];
}

interface ResearchSingularityEngineProps {
  userId?: string;
  searchQuery?: string;
  autoUpdate?: boolean;
  onNewResearch?: (article: ResearchArticle) => void;
  enableAIAnalysis?: boolean;
  enableRealTimeAlerts?: boolean;
}

const ResearchSingularityEngine: React.FC<ResearchSingularityEngineProps> = ({
  userId,
  searchQuery = '',
  autoUpdate = true,
  onNewResearch,
  enableAIAnalysis = true,
  enableRealTimeAlerts = true,
}) => {
  const [sources, setSources] = useState<ResearchSource[]>([]);
  const [recentArticles, setRecentArticles] = useState<ResearchArticle[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStats, setProcessingStats] = useState({
    articlesProcessed: 0,
    totalSources: 6,
    averageProcessingTime: 25, // seconds
    accuracy: 0.95,
  });
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [aiInsights, setAiInsights] = useState<any[]>([]);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [researchAlerts, setResearchAlerts] = useState<any[]>([]);
  const [analysisMode, setAnalysisMode] = useState<'basic' | 'advanced' | 'cosmic'>('cosmic');
  const [realTimeStats, setRealTimeStats] = useState({
    newPapersToday: 0,
    relevantFindings: 0,
    safetyAlerts: 0,
    interactionWarnings: 0,
  });

  // Use the new research AI hook
  const {
    papers: aiPapers,
    insights: aiInsightsData,
    stats: researchStats,
    monitoringData,
    searchLoading: aiSearchLoading,
    insightsLoading,
    monitoringLoading,
    error: aiError,
    searchResearch,
    getPersonalizedInsights,
    startMonitoring,
  } = useResearchAI();

  // Initialize research sources
  useEffect(() => {
    initializeResearchSources();
    if (userId) {
      loadUserProfile();
      // Start monitoring for personalized insights
      if (enableRealTimeAlerts) {
        startMonitoring(userId);
      }
      // Get personalized insights
      if (enableAIAnalysis) {
        getPersonalizedInsights(userId);
      }
    }
    initializeAIInsights();
  }, [userId, enableRealTimeAlerts, enableAIAnalysis, startMonitoring, getPersonalizedInsights]);

  // Auto-update research
  useEffect(() => {
    if (autoUpdate) {
      const interval = setInterval(() => {
        processNewResearch();
      }, 30000); // Every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoUpdate]);

  // Update AI insights when new data arrives
  useEffect(() => {
    if (aiInsightsData.length > 0) {
      setAiInsights(aiInsightsData);
    }
  }, [aiInsightsData]);

  // Update monitoring data when available
  useEffect(() => {
    if (monitoringData) {
      setRealTimeStats(monitoringData.stats || realTimeStats);
      if (monitoringData.alerts) {
        setResearchAlerts(monitoringData.alerts);
      }
    }
  }, [monitoringData]);

  const initializeResearchSources = () => {
    const initialSources: ResearchSource[] = [
      {
        id: 'pubmed',
        name: 'PubMed/MEDLINE',
        type: 'pubmed',
        articles: 35000000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 150,
      },
      {
        id: 'cochrane',
        name: 'Cochrane Library',
        type: 'cochrane',
        articles: 850000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 80,
      },
      {
        id: 'clinicaltrials',
        name: 'ClinicalTrials.gov',
        type: 'clinicaltrials',
        articles: 400000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 120,
      },
      {
        id: 'fda',
        name: 'FDA Database',
        type: 'fda',
        articles: 250000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 90,
      },
      {
        id: 'ema',
        name: 'EMA Database',
        type: 'ema',
        articles: 180000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 75,
      },
      {
        id: 'health_canada',
        name: 'Health Canada',
        type: 'health_canada',
        articles: 120000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 60,
      },
    ];

    setSources(initialSources);
  };

  const loadUserProfile = async () => {
    if (!userId) return;

    try {
      const response = await fetch(`/api/profile/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setUserProfile(data.data);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
    }
  };

  const initializeAIInsights = async () => {
    try {
      // Simulate AI analysis initialization
      await new Promise(resolve => setTimeout(resolve, 1000));

      const insights = [
        {
          id: 'insight-1',
          type: 'breakthrough',
          title: 'New Vitamin D3 Dosing Research',
          content: 'Recent meta-analysis suggests optimal dosing may be higher than previously thought for immune function.',
          confidence: 94,
          impact: 'high',
          relevantSupplements: ['Vitamin D3'],
          timestamp: new Date().toISOString(),
        },
        {
          id: 'insight-2',
          type: 'safety',
          title: 'Magnesium-Drug Interaction Alert',
          content: 'New research identifies potential interaction between high-dose magnesium and certain antibiotics.',
          confidence: 87,
          impact: 'moderate',
          relevantSupplements: ['Magnesium'],
          timestamp: new Date().toISOString(),
        },
        {
          id: 'insight-3',
          type: 'efficacy',
          title: 'Omega-3 Cognitive Benefits Confirmed',
          content: 'Large-scale study confirms cognitive benefits of EPA/DHA supplementation in healthy adults.',
          confidence: 91,
          impact: 'high',
          relevantSupplements: ['Omega-3'],
          timestamp: new Date().toISOString(),
        },
      ];

      setAiInsights(insights);

      // Update real-time stats
      setRealTimeStats({
        newPapersToday: 47,
        relevantFindings: 12,
        safetyAlerts: 2,
        interactionWarnings: 1,
      });

    } catch (error) {
      console.error('Failed to initialize AI insights:', error);
    }
  };

  const performAIAnalysis = async (articles: ResearchArticle[]) => {
    if (!enableAIAnalysis) return;

    try {
      // Simulate AI analysis of research articles
      const analysisPromises = articles.map(async (article) => {
        await new Promise(resolve => setTimeout(resolve, 500));

        return {
          articleId: article.id,
          keyFindings: [
            'Significant improvement in primary outcome measures',
            'No serious adverse events reported',
            'Dose-response relationship observed',
          ],
          clinicalRelevance: Math.random() > 0.5 ? 'high' : 'moderate',
          safetyProfile: Math.random() > 0.8 ? 'concerning' : 'acceptable',
          recommendationLevel: Math.floor(Math.random() * 5) + 1,
          personalizedInsights: userProfile ? generatePersonalizedInsights(article, userProfile) : null,
        };
      });

      const analyses = await Promise.all(analysisPromises);

      // Update AI insights based on new analyses
      const newInsights = analyses
        .filter(analysis => analysis.clinicalRelevance === 'high')
        .map(analysis => ({
          id: `ai-insight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'analysis',
          title: 'AI Analysis Complete',
          content: `High clinical relevance identified in recent research with recommendation level ${analysis.recommendationLevel}/5`,
          confidence: 85 + Math.random() * 10,
          impact: 'high',
          timestamp: new Date().toISOString(),
        }));

      setAiInsights(prev => [...newInsights, ...prev].slice(0, 10));

    } catch (error) {
      console.error('AI analysis failed:', error);
    }
  };

  const generatePersonalizedInsights = (article: ResearchArticle, profile: any) => {
    const insights = [];

    // Check if article supplements match user's current supplements
    const userSupplements = profile.currentSupplements || [];
    const articleSupplements = article.extractedData.supplements;
    const matchingSupplements = articleSupplements.filter(supp =>
      userSupplements.some((userSupp: string) =>
        userSupp.toLowerCase().includes(supp.toLowerCase())
      )
    );

    if (matchingSupplements.length > 0) {
      insights.push({
        type: 'personal_relevance',
        message: `This research is highly relevant to your current supplements: ${matchingSupplements.join(', ')}`,
        priority: 'high',
      });
    }

    // Check if article conditions match user's health goals
    const userGoals = profile.healthGoals || [];
    const articleConditions = article.extractedData.conditions;
    const matchingConditions = articleConditions.filter(condition =>
      userGoals.some((goal: string) =>
        goal.toLowerCase().includes(condition.toLowerCase()) ||
        condition.toLowerCase().includes(goal.toLowerCase())
      )
    );

    if (matchingConditions.length > 0) {
      insights.push({
        type: 'goal_alignment',
        message: `This research addresses your health goals: ${matchingConditions.join(', ')}`,
        priority: 'medium',
      });
    }

    return insights;
  };

  const generateResearchAlert = (article: ResearchArticle) => {
    if (!enableRealTimeAlerts) return;

    const alert = {
      id: `alert-${Date.now()}`,
      type: 'new_research',
      title: 'New Relevant Research Found',
      message: `New study published: "${article.title}"`,
      article: article,
      timestamp: new Date().toISOString(),
      priority: article.relevanceScore > 0.9 ? 'high' : 'medium',
      read: false,
    };

    setResearchAlerts(prev => [alert, ...prev].slice(0, 20));
  };

  const processNewResearch = useCallback(async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    
    try {
      // Simulate real-time research processing
      const processingPromises = sources.map(async (source) => {
        // Update source status
        setSources(prev => prev.map(s => 
          s.id === source.id ? { ...s, status: 'processing' as const } : s
        ));

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        // Generate sample research article
        const article = await generateSampleArticle(source);
        
        // Update source status
        setSources(prev => prev.map(s => 
          s.id === source.id ? { 
            ...s, 
            status: 'active' as const,
            lastUpdate: new Date()
          } : s
        ));

        return article;
      });

      const newArticles = await Promise.all(processingPromises);
      
      // Add new articles to the list
      setRecentArticles(prev => [...newArticles, ...prev].slice(0, 20));

      // Perform AI analysis on new articles
      await performAIAnalysis(newArticles);

      // Generate alerts for highly relevant articles
      newArticles.forEach(article => {
        if (article.relevanceScore > 0.85) {
          generateResearchAlert(article);
        }
      });

      // Update processing stats
      setProcessingStats(prev => ({
        ...prev,
        articlesProcessed: prev.articlesProcessed + newArticles.length,
      }));

      // Notify parent component
      newArticles.forEach(article => {
        onNewResearch?.(article);
      });

    } catch (error) {
      console.error('Error processing research:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [sources, onNewResearch, isProcessing]);

  const generateSampleArticle = async (source: ResearchSource): Promise<ResearchArticle> => {
    const sampleTitles = [
      'Vitamin D3 supplementation and immune function: A randomized controlled trial',
      'Magnesium glycinate effects on sleep quality in adults with insomnia',
      'Omega-3 fatty acids and cognitive performance: Meta-analysis of 15 studies',
      'Ashwagandha root extract for stress reduction: Clinical evidence',
      'Curcumin bioavailability enhancement with piperine: Pharmacokinetic study',
      'Probiotics and gut microbiome diversity: Systematic review',
    ];

    const sampleJournals = [
      'Journal of Clinical Medicine',
      'Nutrients',
      'American Journal of Clinical Nutrition',
      'Phytotherapy Research',
      'Clinical Nutrition',
      'Frontiers in Nutrition',
    ];

    const processingTime = Math.random() * 20 + 10; // 10-30 seconds

    return {
      id: `article-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: sampleTitles[Math.floor(Math.random() * sampleTitles.length)],
      authors: ['Smith J.', 'Johnson A.', 'Williams B.'],
      journal: sampleJournals[Math.floor(Math.random() * sampleJournals.length)],
      year: 2024,
      pmid: `${38000000 + Math.floor(Math.random() * 1000000)}`,
      doi: `10.1000/${Math.random().toString(36).substr(2, 9)}`,
      abstract: 'This study investigates the effects of supplement intervention on health outcomes...',
      relevanceScore: 0.8 + Math.random() * 0.2,
      qualityScore: 0.7 + Math.random() * 0.3,
      extractedData: {
        supplements: ['Vitamin D3', 'Magnesium'],
        conditions: ['Immune dysfunction', 'Sleep disorders'],
        outcomes: ['Improved immune markers', 'Better sleep quality'],
        dosages: ['2000 IU daily', '400mg before bed'],
        sideEffects: ['Mild gastrointestinal upset'],
        interactions: ['None reported'],
        studyType: 'rct',
        participants: Math.floor(Math.random() * 1000) + 100,
        duration: '12 weeks',
        conclusions: ['Significant improvement in primary outcomes'],
      },
      processingTime,
      confidence: 0.9 + Math.random() * 0.1,
    };
  };

  const handleSearch = async () => {
    if (!searchInput.trim()) return;

    setIsProcessing(true);

    try {
      // Use AI-powered research search if available
      if (searchResearch) {
        await searchResearch({
          supplement: searchInput,
          minEvidenceLevel: analysisMode === 'cosmic' ? 4 : analysisMode === 'advanced' ? 3 : 1,
          includeMetaAnalysis: analysisMode !== 'basic',
          maxResults: 20,
        }, userId);

        // If we have AI papers, merge them with existing articles
        if (aiPapers.length > 0) {
          const newArticles = aiPapers.map(paper => ({
            id: paper.id,
            title: paper.title,
            authors: paper.authors,
            journal: paper.journal,
            year: paper.year,
            abstract: paper.abstract,
            extractedData: {
              studyType: paper.studyType,
              participants: paper.findings?.confidence || 100,
              supplements: paper.supplements,
              conditions: paper.conditions,
              outcomes: paper.findings?.secondary || [],
            },
            relevanceScore: paper.relevanceScore,
            confidence: paper.findings?.confidence / 100 || 0.9,
            processingTime: Math.random() * 3 + 1,
          }));

          setRecentArticles(prev => [...newArticles, ...prev].slice(0, 20));
        }

        // Update AI insights
        if (aiInsightsData.length > 0) {
          setAiInsights(aiInsightsData);
        }

        // Update processing stats
        setProcessingStats(prev => ({
          ...prev,
          articlesProcessed: prev.articlesProcessed + (aiPapers.length || 5),
          accuracy: researchStats?.avgConfidence / 100 || prev.accuracy,
        }));
      } else {
        // Fallback to original search simulation
        await new Promise(resolve => setTimeout(resolve, 2000));

        const searchResults = await Promise.all(
          sources.slice(0, 3).map(source => generateSampleArticle(source))
        );

        setRecentArticles(searchResults);
      }

    } catch (error) {
      console.error('Error searching research:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'pubmed':
        return '🔬';
      case 'cochrane':
        return '📊';
      case 'clinicaltrials':
        return '🧪';
      case 'fda':
        return '🏛️';
      case 'ema':
        return '🇪🇺';
      case 'health_canada':
        return '🇨🇦';
      default:
        return '📄';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-secondary-600 bg-secondary-100';
      case 'processing':
        return 'text-warning-600 bg-warning-100';
      case 'error':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStudyTypeIcon = (type: string) => {
    switch (type) {
      case 'rct':
        return '🎯';
      case 'meta_analysis':
        return '📈';
      case 'observational':
        return '👁️';
      case 'review':
        return '📚';
      case 'case_study':
        return '📋';
      default:
        return '📄';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                rotate: { duration: 8, repeat: Infinity, ease: 'linear' },
                scale: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <GlobeAltIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🌐 Research Singularity Engine
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time integration of 35M+ research articles with &lt;30s processing
              </p>
            </div>
          </div>

          {/* Processing Stats */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-secondary-600">
                {processingStats.articlesProcessed}
              </div>
              <div className="text-xs text-gray-500">Articles Processed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {processingStats.averageProcessingTime}s
              </div>
              <div className="text-xs text-gray-500">Avg Processing</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-warning-600">
                {Math.round(processingStats.accuracy * 100)}%
              </div>
              <div className="text-xs text-gray-500">AI Accuracy</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {processingStats.totalSources}
              </div>
              <div className="text-xs text-gray-500">Data Sources</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Search Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="cosmic-card mb-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          🔍 Quantum Research Search
        </h2>
        
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search 35M+ research articles..."
              className="cosmic-input pl-10"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={isProcessing}
            className="cosmic-button cosmic-button--primary"
          >
            <BoltIcon className="w-5 h-5 mr-2" />
            Search
          </button>
          <button
            onClick={processNewResearch}
            disabled={isProcessing}
            className="cosmic-button cosmic-button--outline"
          >
            <SparklesIcon className="w-5 h-5 mr-2" />
            Update
          </button>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Data Sources */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📡 Data Sources
          </h2>
          
          <div className="space-y-3">
            {sources.map((source) => (
              <div key={source.id} className="cosmic-card bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getSourceIcon(source.type)}</span>
                    <span className="font-medium text-gray-900">{source.name}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(source.status)}`}>
                    {source.status}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">{source.articles.toLocaleString()}</span>
                    <span className="text-gray-500"> articles</span>
                  </div>
                  <div>
                    <span className="font-medium">{source.processingSpeed}</span>
                    <span className="text-gray-500"> /sec</span>
                  </div>
                </div>
                
                {source.status === 'processing' && (
                  <div className="mt-2">
                    <div className="cosmic-progress">
                      <motion.div 
                        className="cosmic-progress__bar"
                        initial={{ width: 0 }}
                        animate={{ width: '100%' }}
                        transition={{ duration: 3, ease: 'easeInOut' }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Recent Research */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                📚 Latest Research
              </h2>
              {isProcessing && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="cosmic-spin w-4 h-4 border-2 border-primary-200 border-t-primary-600 rounded-full"></div>
                  <span>Processing...</span>
                </div>
              )}
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              <AnimatePresence>
                {recentArticles.map((article, index) => (
                  <motion.div
                    key={article.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="cosmic-card cosmic-card--health"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
                          {article.title}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                          <span>{getStudyTypeIcon(article.extractedData.studyType)}</span>
                          <span>{article.journal}</span>
                          <span>•</span>
                          <span>{article.year}</span>
                          <span>•</span>
                          <span>{article.extractedData.participants} participants</span>
                        </div>
                      </div>
                      
                      <div className="text-right ml-4">
                        <div className="text-lg font-bold text-secondary-600">
                          {Math.round(article.relevanceScore * 100)}%
                        </div>
                        <div className="text-xs text-gray-500">Relevance</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Supplements:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {article.extractedData.supplements.slice(0, 2).map((supp, idx) => (
                            <span key={idx} className="cosmic-badge cosmic-badge--primary text-xs">
                              {supp}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <span className="font-medium text-gray-700">Outcomes:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {article.extractedData.outcomes.slice(0, 2).map((outcome, idx) => (
                            <span key={idx} className="cosmic-badge cosmic-badge--success text-xs">
                              {outcome}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="w-3 h-3" />
                          <span>{article.processingTime.toFixed(1)}s</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <CheckBadgeIcon className="w-3 h-3" />
                          <span>{Math.round(article.confidence * 100)}% confidence</span>
                        </div>
                      </div>
                      
                      <button className="cosmic-button cosmic-button--outline text-xs py-1 px-2">
                        View Details
                      </button>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {recentArticles.length === 0 && !isProcessing && (
              <div className="text-center py-8 text-gray-500">
                <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No research articles yet</p>
                <p className="text-sm">Start processing to see latest research</p>
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* AI Insights Section */}
      {enableAIAnalysis && aiInsights.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="cosmic-card mt-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <SparklesIcon className="w-6 h-6 text-primary-500 mr-2" />
            🧠 AI Research Insights
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {aiInsights.slice(0, 6).map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className={`cosmic-card ${
                  insight.type === 'breakthrough' ? 'cosmic-card--success' :
                  insight.type === 'safety' ? 'cosmic-card--warning' :
                  insight.type === 'efficacy' ? 'cosmic-card--primary' :
                  'cosmic-card--health'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <span className={`cosmic-badge text-xs ${
                    insight.type === 'breakthrough' ? 'cosmic-badge--success' :
                    insight.type === 'safety' ? 'cosmic-badge--warning' :
                    insight.type === 'efficacy' ? 'cosmic-badge--primary' :
                    'cosmic-badge--outline'
                  }`}>
                    {insight.type}
                  </span>
                  <div className="text-sm text-gray-500">
                    {insight.confidence}% confidence
                  </div>
                </div>

                <h3 className="font-semibold text-gray-900 mb-2">{insight.title}</h3>
                <p className="text-sm text-gray-600 mb-3">{insight.content}</p>

                {insight.relevantSupplements && (
                  <div className="flex flex-wrap gap-1">
                    {insight.relevantSupplements.map((supp: string, idx: number) => (
                      <span key={idx} className="cosmic-badge cosmic-badge--outline text-xs">
                        {supp}
                      </span>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Real-time Stats Dashboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6"
      >
        <div className="cosmic-card text-center">
          <DocumentTextIcon className="w-8 h-8 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{realTimeStats.newPapersToday}</div>
          <div className="text-sm text-gray-600">New Papers Today</div>
        </div>

        <div className="cosmic-card text-center">
          <CheckBadgeIcon className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{realTimeStats.relevantFindings}</div>
          <div className="text-sm text-gray-600">Relevant Findings</div>
        </div>

        <div className="cosmic-card text-center">
          <ExclamationTriangleIcon className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{realTimeStats.safetyAlerts}</div>
          <div className="text-sm text-gray-600">Safety Alerts</div>
        </div>

        <div className="cosmic-card text-center">
          <BoltIcon className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">{realTimeStats.interactionWarnings}</div>
          <div className="text-sm text-gray-600">Interaction Warnings</div>
        </div>
      </motion.div>

      {/* Research Alerts */}
      {enableRealTimeAlerts && researchAlerts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="cosmic-card mt-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <BoltIcon className="w-6 h-6 text-yellow-500 mr-2" />
            🚨 Real-time Research Alerts
          </h2>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {researchAlerts.slice(0, 10).map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`cosmic-card ${
                  alert.priority === 'high' ? 'border-l-4 border-red-500' :
                  alert.priority === 'medium' ? 'border-l-4 border-yellow-500' :
                  'border-l-4 border-blue-500'
                } ${alert.read ? 'opacity-60' : ''}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`cosmic-badge text-xs ${
                        alert.priority === 'high' ? 'cosmic-badge--error' :
                        alert.priority === 'medium' ? 'cosmic-badge--warning' :
                        'cosmic-badge--primary'
                      }`}>
                        {alert.priority}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(alert.timestamp).toLocaleTimeString()}
                      </span>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-1">{alert.title}</h3>
                    <p className="text-sm text-gray-600">{alert.message}</p>
                  </div>

                  <button className="cosmic-button cosmic-button--outline text-xs py-1 px-2 ml-4">
                    View
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* User Profile Integration */}
      {userProfile && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="cosmic-card mt-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <BeakerIcon className="w-6 h-6 text-purple-500 mr-2" />
            👤 Personalized Research Insights
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Your Current Supplements</h3>
              <div className="flex flex-wrap gap-2">
                {(userProfile.currentSupplements || []).slice(0, 6).map((supp: string, idx: number) => (
                  <span key={idx} className="cosmic-badge cosmic-badge--primary text-sm">
                    {supp}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Research Relevance</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Supplement Match</span>
                  <span className="font-medium">87%</span>
                </div>
                <div className="cosmic-progress">
                  <div className="cosmic-progress__bar" style={{ width: '87%' }} />
                </div>

                <div className="flex justify-between text-sm">
                  <span>Goal Alignment</span>
                  <span className="font-medium">73%</span>
                </div>
                <div className="cosmic-progress">
                  <div className="cosmic-progress__bar" style={{ width: '73%' }} />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ResearchSingularityEngine;
