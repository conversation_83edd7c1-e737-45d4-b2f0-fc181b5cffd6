import { useState, useEffect, useCallback } from 'react';

interface ResearchQuery {
  supplement?: string;
  condition?: string;
  studyType?: string;
  minYear?: number;
  minEvidenceLevel?: number;
  includeMetaAnalysis?: boolean;
  maxResults?: number;
}

interface ResearchPaper {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  year: number;
  abstract: string;
  studyType: string;
  evidenceLevel: number;
  findings: {
    primary: string;
    secondary: string[];
    significance: string;
    confidence: number;
  };
  supplements: string[];
  conditions: string[];
  qualityScore: number;
  relevanceScore: number;
  citationCount: number;
}

interface AIInsight {
  id: string;
  type: string;
  title: string;
  content: string;
  confidence: number;
  impact: string;
  relevantSupplements: string[];
}

interface ResearchStats {
  totalPapers: number;
  avgQualityScore: number;
  avgEvidenceLevel: number;
  avgConfidence: number;
  studyTypeDistribution: Record<string, number>;
}

interface UseResearchAIReturn {
  // Data
  papers: ResearchPaper[];
  insights: AIInsight[];
  stats: ResearchStats | null;
  monitoringData: any;
  
  // Loading states
  loading: boolean;
  searchLoading: boolean;
  insightsLoading: boolean;
  monitoringLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Functions
  searchResearch: (query: ResearchQuery, userId?: string) => Promise<void>;
  getPersonalizedInsights: (userId: string) => Promise<void>;
  analyzeInteractions: (supplementIds: string[], userId?: string) => Promise<any>;
  startMonitoring: (userId: string) => Promise<void>;
  generateReport: (userId: string, options?: any) => Promise<any>;
  clearResults: () => void;
}

export const useResearchAI = (): UseResearchAIReturn => {
  const [papers, setPapers] = useState<ResearchPaper[]>([]);
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [stats, setStats] = useState<ResearchStats | null>(null);
  const [monitoringData, setMonitoringData] = useState<any>(null);
  
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [monitoringLoading, setMonitoringLoading] = useState(false);
  
  const [error, setError] = useState<string | null>(null);

  const searchResearch = useCallback(async (query: ResearchQuery, userId?: string) => {
    setSearchLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/research-ai/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...query,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Research search failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setPapers(data.data.papers);
        setInsights(data.data.insights);
        setStats(data.data.stats);
      } else {
        throw new Error(data.message || 'Research search failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Research search error:', err);
    } finally {
      setSearchLoading(false);
    }
  }, []);

  const getPersonalizedInsights = useCallback(async (userId: string) => {
    setInsightsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/research-ai/insights/${userId}?limit=20`);

      if (!response.ok) {
        throw new Error(`Failed to get insights: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setInsights(data.data);
      } else {
        throw new Error(data.message || 'Failed to get insights');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Insights error:', err);
    } finally {
      setInsightsLoading(false);
    }
  }, []);

  const analyzeInteractions = useCallback(async (supplementIds: string[], userId?: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/research-ai/analyze-interactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supplementIds,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Interaction analysis failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Interaction analysis failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Interaction analysis error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const startMonitoring = useCallback(async (userId: string) => {
    setMonitoringLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/research-ai/monitor/${userId}`);

      if (!response.ok) {
        throw new Error(`Monitoring failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setMonitoringData(data.data);
      } else {
        throw new Error(data.message || 'Monitoring failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Monitoring error:', err);
    } finally {
      setMonitoringLoading(false);
    }
  }, []);

  const generateReport = useCallback(async (userId: string, options: any = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/research-ai/generate-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          ...options,
        }),
      });

      if (!response.ok) {
        throw new Error(`Report generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Report generation failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Report generation error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setPapers([]);
    setInsights([]);
    setStats(null);
    setMonitoringData(null);
    setError(null);
  }, []);

  // Auto-refresh monitoring data every 30 seconds if monitoring is active
  useEffect(() => {
    if (monitoringData) {
      const interval = setInterval(() => {
        // Refresh monitoring data silently
        console.log('🔄 Refreshing monitoring data...');
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [monitoringData]);

  return {
    // Data
    papers,
    insights,
    stats,
    monitoringData,
    
    // Loading states
    loading,
    searchLoading,
    insightsLoading,
    monitoringLoading,
    
    // Error state
    error,
    
    // Functions
    searchResearch,
    getPersonalizedInsights,
    analyzeInteractions,
    startMonitoring,
    generateReport,
    clearResults,
  };
};

export default useResearchAI;
