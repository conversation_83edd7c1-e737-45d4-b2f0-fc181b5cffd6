import dotenv from 'dotenv';
import Jo<PERSON> from 'joi';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  PORT: Joi.number().default(3069),
  
  // Neo4j Configuration
  NEO4J_URI: Joi.string().required(),
  NEO4J_USERNAME: Joi.string().required(),
  NEO4J_PASSWORD: Joi.string().required(),
  
  // Weaviate Configuration
  WEAVIATE_URL: Joi.string().uri().required(),
  WEAVIATE_API_KEY: Joi.string().optional(),
  
  // Redis Configuration
  REDIS_URL: Joi.string().required(),
  REDIS_PASSWORD: Joi.string().optional(),
  
  // MongoDB Configuration
  MONGODB_URI: Joi.string().required(),
  
  // AI API Keys
  GEMINI_API_KEY: Joi.string().required(),
  OPENAI_API_KEY: Joi.string().optional(),
  ANTHROPIC_API_KEY: Joi.string().optional(),

  // LM Studio Configuration (OpenAI-compatible)
  LM_STUDIO_URL: Joi.string().uri().optional(),
  LM_STUDIO_API_KEY: Joi.string().optional(),

  // Ollama Configuration (optional)
  OLLAMA_URL: Joi.string().uri().optional(),

  // Research API Keys
  BRAVE_API_KEY: Joi.string().optional(),
  TAVILY_API_KEY: Joi.string().optional(),

  // Medical AI Configuration
  GEMMA_MEDICAL_URL: Joi.string().uri().default('http://*************:1234'),
  
  // Security
  JWT_SECRET: Joi.string().min(32).default('your-super-secret-jwt-key-change-this-in-production'),
  JWT_EXPIRES_IN: Joi.string().default('7d'),
  
  // CORS
  CORS_ORIGINS: Joi.string().default('http://localhost:5174,http://localhost:3069'),
  
  // File Upload
  MAX_FILE_SIZE: Joi.number().default(50 * 1024 * 1024), // 50MB
  UPLOAD_DIR: Joi.string().default('./uploads'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().default(15 * 60 * 1000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // Logging
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('info'),
  LOG_FILE: Joi.string().default('./logs/app.log'),
  
  // Cache TTL (Time To Live) in seconds
  CACHE_TTL: Joi.number().default(3600), // 1 hour
  
  // AI Model Configuration
  DEFAULT_AI_MODEL: Joi.string().default('gemini-pro'),
  MAX_TOKENS: Joi.number().default(4096),
  TEMPERATURE: Joi.number().min(0).max(2).default(0.7),
  
  // Knowledge Graph Configuration
  MAX_GRAPH_NODES: Joi.number().default(10000),
  MAX_GRAPH_RELATIONSHIPS: Joi.number().default(50000),
  GRAPH_EXPANSION_LIMIT: Joi.number().default(100),
  
  // RAG Configuration
  EMBEDDING_MODEL: Joi.string().default('text-embedding-ada-002'),
  VECTOR_DIMENSION: Joi.number().default(1536),
  SIMILARITY_THRESHOLD: Joi.number().min(0).max(1).default(0.7),
  MAX_CONTEXT_LENGTH: Joi.number().default(8000),
  
  // Crawling Configuration
  CRAWL_DELAY_MS: Joi.number().default(1000),
  MAX_CRAWL_DEPTH: Joi.number().default(3),
  MAX_PAGES_PER_DOMAIN: Joi.number().default(100),
  
  // Health Check Configuration
  HEALTH_CHECK_INTERVAL: Joi.number().default(30000), // 30 seconds
}).unknown();

// Validate environment variables
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

// Export configuration object
export const config = {
  // Environment
  nodeEnv: envVars.NODE_ENV as string,
  isDevelopment: envVars.NODE_ENV === 'development',
  isProduction: envVars.NODE_ENV === 'production',
  isTest: envVars.NODE_ENV === 'test',
  port: envVars.PORT as number,
  
  // Database URLs
  neo4j: {
    uri: envVars.NEO4J_URI as string,
    username: envVars.NEO4J_USERNAME as string,
    password: envVars.NEO4J_PASSWORD as string,
  },
  
  weaviate: {
    url: envVars.WEAVIATE_URL as string,
    apiKey: envVars.WEAVIATE_API_KEY as string | undefined,
  },
  
  redis: {
    url: envVars.REDIS_URL as string,
    password: envVars.REDIS_PASSWORD as string | undefined,
  },
  
  mongodb: {
    uri: envVars.MONGODB_URI as string,
  },
  
  // AI Configuration
  ai: {
    geminiApiKey: envVars.GEMINI_API_KEY as string,
    openaiApiKey: envVars.OPENAI_API_KEY as string | undefined,
    anthropicApiKey: envVars.ANTHROPIC_API_KEY as string | undefined,
    lmStudioUrl: envVars.LM_STUDIO_URL as string | undefined,
    lmStudioApiKey: envVars.LM_STUDIO_API_KEY as string | undefined,
    ollamaUrl: envVars.OLLAMA_URL as string | undefined,
    defaultModel: envVars.DEFAULT_AI_MODEL as string,
    maxTokens: envVars.MAX_TOKENS as number,
    temperature: envVars.TEMPERATURE as number,
  },

  // Research Configuration
  research: {
    braveApiKey: envVars.BRAVE_API_KEY as string | undefined,
    tavilyApiKey: envVars.TAVILY_API_KEY as string | undefined,
    gemmaMedicalUrl: envVars.GEMMA_MEDICAL_URL as string,
  },
  
  // Security
  jwt: {
    secret: envVars.JWT_SECRET as string,
    expiresIn: envVars.JWT_EXPIRES_IN as string,
  },
  
  // CORS
  corsOrigins: (envVars.CORS_ORIGINS as string).split(',').map(origin => origin.trim()),
  
  // File Upload
  upload: {
    maxFileSize: envVars.MAX_FILE_SIZE as number,
    uploadDir: envVars.UPLOAD_DIR as string,
  },
  
  // Rate Limiting
  rateLimit: {
    windowMs: envVars.RATE_LIMIT_WINDOW_MS as number,
    maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS as number,
  },
  
  // Logging
  logging: {
    level: envVars.LOG_LEVEL as string,
    file: envVars.LOG_FILE as string,
  },
  
  // Cache
  cache: {
    ttl: envVars.CACHE_TTL as number,
  },
  
  // Knowledge Graph
  graph: {
    maxNodes: envVars.MAX_GRAPH_NODES as number,
    maxRelationships: envVars.MAX_GRAPH_RELATIONSHIPS as number,
    expansionLimit: envVars.GRAPH_EXPANSION_LIMIT as number,
  },
  
  // RAG
  rag: {
    embeddingModel: envVars.EMBEDDING_MODEL as string,
    vectorDimension: envVars.VECTOR_DIMENSION as number,
    similarityThreshold: envVars.SIMILARITY_THRESHOLD as number,
    maxContextLength: envVars.MAX_CONTEXT_LENGTH as number,
  },
  
  // Crawling
  crawling: {
    delayMs: envVars.CRAWL_DELAY_MS as number,
    maxDepth: envVars.MAX_CRAWL_DEPTH as number,
    maxPagesPerDomain: envVars.MAX_PAGES_PER_DOMAIN as number,
  },
  
  // Health Check
  healthCheck: {
    interval: envVars.HEALTH_CHECK_INTERVAL as number,
  },
} as const;

// Export types for TypeScript
export type Config = typeof config;
export type NodeEnv = typeof config.nodeEnv;
