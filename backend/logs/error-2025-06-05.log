{"address": "::", "code": "EADDRINUSE", "errno": -98, "level": "error", "message": "❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000", "port": 3000, "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:203:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)", "syscall": "listen", "timestamp": "2025-06-05 04:35:13:3513"}